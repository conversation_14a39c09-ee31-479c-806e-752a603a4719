"""
NRC 8th Edition (NASEM 2021) Nutrient Requirements for Dairy Cattle

This module implements the equations and models from the 8th revised edition of
the Nutrient Requirements of Dairy Cattle (NASEM, 2021) for calculating nutrient
requirements based on animal characteristics.

This implementation uses coefficients stored in the database.
"""

import math
import logging
from .nrc_coefficient_service import get_coefficient_value, get_coefficients_by_group

logger = logging.getLogger(__name__)

def calculate_dry_matter_intake(animal_data):
    """
    Calculate dry matter intake (DMI) using NRC 8th edition equations

    Args:
        animal_data: Dictionary containing animal characteristics
            - weight_kg: Body weight in kg
            - milk_production_kg: Milk production in kg/day
            - days_in_milk: Days in milk
            - lactation_number: Lactation number
            - bcs: Body condition score (1-5 scale)

    Returns:
        float: Estimated dry matter intake in kg/day
    """
    # Extract required parameters with defaults if not provided
    weight_kg = animal_data.get('weight_kg', 0)
    milk_kg = animal_data.get('milk_production_kg', 0)
    dim = animal_data.get('days_in_milk', 0)
    lactation = animal_data.get('lactation_number', 1)

    # Basic validation
    if weight_kg <= 0:
        weight_kg = 680

    try:
        # For lactating cows
        if milk_kg > 0 and dim > 0:
            # Get coefficients from database
            multi_base = get_coefficient_value('dmi_multi_base', 'Dry Matter Intake', animal_type='lactating')
            milk_coef = get_coefficient_value('dmi_milk_coef', 'Dry Matter Intake', animal_type='lactating')
            prim_factor = get_coefficient_value('dmi_prim_factor', 'Dry Matter Intake', animal_type='lactating')
            early_base = get_coefficient_value('dmi_early_base', 'Dry Matter Intake', animal_type='lactating')
            early_exp = get_coefficient_value('dmi_early_exp', 'Dry Matter Intake', animal_type='lactating')

            # Use default values if coefficients not found
            multi_base = 0.14 if multi_base is None else multi_base
            milk_coef = 0.15 if milk_coef is None else milk_coef
            prim_factor = 0.88 if prim_factor is None else prim_factor
            early_base = 1.7 if early_base is None else early_base
            early_exp = -0.07 if early_exp is None else early_exp

            # Base equation for multiparous cows (lactation >= 2)
            if lactation >= 2:
                # NASEM 2021 equation for multiparous cows
                dmi = (multi_base * weight_kg**0.75 + milk_coef * milk_kg)

                # Adjustment for days in milk (DIM)
                if dim < 150:
                    # Early lactation adjustment
                    dim_factor = 1 - (early_base * math.exp(early_exp * dim))
                    dmi *= dim_factor
            else:
                # Primiparous cows (first lactation)
                # NASEM 2021 equation for primiparous cows (lactation = 1)
                dmi = (multi_base * weight_kg**0.75 + milk_coef * milk_kg) * prim_factor

                # Adjustment for days in milk (DIM)
                if dim < 150:
                    # Early lactation adjustment for primiparous cows
                    dim_factor = 1 - (early_base * math.exp(early_exp * dim))
                    dmi *= dim_factor

        # For dry cows
        elif milk_kg == 0 and dim == 0:
            # Get coefficient from database
            dry_coef = get_coefficient_value('dmi_dry_coef', 'Dry Matter Intake', animal_type='dry')

            # Use default value if coefficient not found
            dry_coef = 0.022 if dry_coef is None else dry_coef

            # NASEM 2021 equation for dry cows
            dmi = dry_coef * weight_kg

        # For growing heifers
        else:
            # Get coefficients from database
            heifer_weight_coef = get_coefficient_value('dmi_heifer_weight_coef', 'Dry Matter Intake', animal_type='heifer')
            heifer_mw_coef = get_coefficient_value('dmi_heifer_mw_coef', 'Dry Matter Intake', animal_type='heifer')

            # Use default values if coefficients not found
            heifer_weight_coef = 0.0185 if heifer_weight_coef is None else heifer_weight_coef
            heifer_mw_coef = 0.2 if heifer_mw_coef is None else heifer_mw_coef

            # NASEM 2021 equation for growing heifers
            dmi = heifer_weight_coef * weight_kg + heifer_mw_coef * (weight_kg**0.75)
    except Exception as e:
        # Log the error and fall back to hardcoded values
        logger.error(f"Error getting DMI coefficients from database: {str(e)}. Using default values.")

        # For lactating cows
        if milk_kg > 0 and dim > 0:
            # Base equation for multiparous cows (lactation >= 2)
            if lactation >= 2:
                # NASEM 2021 equation for multiparous cows
                dmi = (0.14 * weight_kg**0.75 + 0.15 * milk_kg)

                # Adjustment for days in milk (DIM)
                if dim < 150:
                    # Early lactation adjustment
                    dim_factor = 1 - (1.7 * math.exp(-0.07 * dim))
                    dmi *= dim_factor
            else:
                # Primiparous cows (first lactation)
                # NASEM 2021 equation for primiparous cows (lactation = 1)
                dmi = (0.14 * weight_kg**0.75 + 0.15 * milk_kg) * 0.88

                # Adjustment for days in milk (DIM)
                if dim < 150:
                    # Early lactation adjustment for primiparous cows
                    dim_factor = 1 - (1.7 * math.exp(-0.07 * dim))
                    dmi *= dim_factor

        # For dry cows
        elif milk_kg == 0 and dim == 0:
            # NASEM 2021 equation for dry cows
            dmi = 0.022 * weight_kg

        # For growing heifers
        else:
            # NASEM 2021 equation for growing heifers
            dmi = 0.0185 * weight_kg + 0.2 * (weight_kg**0.75)

    return round(dmi, 2)


def calculate_energy_requirements(animal_data):
    """
    Calculate energy requirements using NRC 8th edition equations

    Args:
        animal_data: Dictionary containing animal characteristics
            - weight_kg: Body weight in kg
            - milk_production_kg: Milk production in kg/day
            - milk_fat_percent: Milk fat percentage
            - milk_protein_percent: Milk protein percentage
            - days_in_milk: Days in milk
            - lactation_number: Lactation number
            - bcs: Body condition score (1-5 scale)
            - pregnancy_day: Day of pregnancy (if applicable)

    Returns:
        dict: Energy requirements in different units
            - maintenance_nel: Maintenance NEL requirement (Mcal/day)
            - lactation_nel: Lactation NEL requirement (Mcal/day)
            - pregnancy_nel: Pregnancy NEL requirement (Mcal/day)
            - growth_nel: Growth NEL requirement (Mcal/day)
            - total_nel: Total NEL requirement (Mcal/day)
            - nel_concentration: Required NEL concentration (Mcal/kg DM)
    """
    # Extract required parameters with defaults if not provided
    weight_kg = animal_data.get('weight_kg', 0)
    milk_kg = animal_data.get('milk_production_kg', 0)
    milk_fat = animal_data.get('milk_fat_percent', 3.5)
    milk_protein = animal_data.get('milk_protein_percent', 3.0)
    dim = animal_data.get('days_in_milk', 0)
    lactation = animal_data.get('lactation_number', 1)
    # bcs not used in this function but included for completeness
    bcs = animal_data.get('bcs', 3.0)
    pregnancy_day = animal_data.get('pregnancy_day', 0)

    # Basic validation
    if weight_kg <= 0:
        weight_kg = 680

    try:
        # Get coefficients from database
        energy_coeffs = get_coefficients_by_group('Energy Requirements')

        # Maintenance energy coefficient
        maint_coef = energy_coeffs.get('nel_maint_coef', 0.086)

        # Milk energy coefficients
        milk_fat_coef = energy_coeffs.get('nel_milk_fat_coef', 0.0929)
        milk_protein_coef = energy_coeffs.get('nel_milk_protein_coef', 0.0547)
        milk_lactose_coef = energy_coeffs.get('nel_milk_lactose_coef', 0.0395)
        default_lactose = energy_coeffs.get('default_lactose_percent', 4.85)

        # Pregnancy energy coefficients
        preg_coef = energy_coeffs.get('nel_preg_coef', 0.00318)
        preg_exp = energy_coeffs.get('nel_preg_exp', 0.0352)

        # Growth energy coefficient
        growth_factor = energy_coeffs.get('nel_growth_factor', 0.1)

        # Calculate maintenance energy requirement (NEL)
        maintenance_nel = maint_coef * (weight_kg**0.75)

        # Calculate lactation energy requirement (NEL)
        if milk_kg > 0:
            # Energy content of milk based on fat, protein, and lactose
            milk_energy = (milk_fat_coef * milk_fat +
                          milk_protein_coef * milk_protein +
                          milk_lactose_coef * default_lactose) * milk_kg
            lactation_nel = milk_energy
        else:
            lactation_nel = 0

        # Calculate pregnancy energy requirement (NEL)
        pregnancy_nel = 0
        if pregnancy_day > 190:  # Only significant in last trimester
            # Assumes 45 kg calf birth weight
            calf_birth_weight = 45  # kg
            pregnancy_nel = preg_coef * calf_birth_weight * math.exp(preg_exp * (pregnancy_day - 190))

        # Calculate growth energy requirement (NEL)
        growth_nel = 0
        if lactation == 1 and dim > 0:  # First lactation animals still growing
            growth_nel = growth_factor * maintenance_nel
    except Exception as e:
        # Log the error and fall back to hardcoded values
        logger.error(f"Error getting energy coefficients from database: {str(e)}. Using default values.")

        # Calculate maintenance energy requirement (NEL)
        maintenance_nel = 0.086 * (weight_kg**0.75)

        # Calculate lactation energy requirement (NEL)
        if milk_kg > 0:
            # Energy content of milk based on fat, protein, and lactose
            milk_energy = (0.0929 * milk_fat + 0.0547 * milk_protein + 0.0395 * 4.85) * milk_kg
            lactation_nel = milk_energy
        else:
            lactation_nel = 0

        # Calculate pregnancy energy requirement (NEL)
        pregnancy_nel = 0
        if pregnancy_day > 190:  # Only significant in last trimester
            # Assumes 45 kg calf birth weight
            calf_birth_weight = 45  # kg
            pregnancy_nel = 0.00318 * calf_birth_weight * math.exp(0.0352 * (pregnancy_day - 190))

        # Calculate growth energy requirement (NEL)
        growth_nel = 0
        if lactation == 1 and dim > 0:  # First lactation animals still growing
            growth_nel = 0.1 * maintenance_nel

    # Calculate total NEL requirement
    total_nel = maintenance_nel + lactation_nel + pregnancy_nel + growth_nel

    # Calculate required NEL concentration based on DMI
    dmi = calculate_dry_matter_intake(animal_data)
    nel_concentration = total_nel / dmi if dmi > 0 else 0

    return {
        'maintenance_nel': round(maintenance_nel, 2),
        'lactation_nel': round(lactation_nel, 2),
        'pregnancy_nel': round(pregnancy_nel, 2),
        'growth_nel': round(growth_nel, 2),
        'total_nel': round(total_nel, 2),
        'nel_concentration': round(nel_concentration, 2)
    }


def calculate_protein_requirements(animal_data):
    """
    Calculate protein requirements using NRC 8th edition equations

    Args:
        animal_data: Dictionary containing animal characteristics
            - weight_kg: Body weight in kg
            - milk_production_kg: Milk production in kg/day
            - milk_protein_percent: Milk protein percentage
            - days_in_milk: Days in milk
            - lactation_number: Lactation number
            - pregnancy_day: Day of pregnancy (if applicable)

    Returns:
        dict: Protein requirements
            - maintenance_mp: Maintenance MP requirement (g/day)
            - lactation_mp: Lactation MP requirement (g/day)
            - pregnancy_mp: Pregnancy MP requirement (g/day)
            - growth_mp: Growth MP requirement (g/day)
            - total_mp: Total MP requirement (g/day)
            - crude_protein_percent: Required crude protein (% of DM)
    """
    # Extract required parameters with defaults if not provided
    weight_kg = animal_data.get('weight_kg', 0)
    milk_kg = animal_data.get('milk_production_kg', 0)
    milk_protein = animal_data.get('milk_protein_percent', 3.0)
    dim = animal_data.get('days_in_milk', 0)
    lactation = animal_data.get('lactation_number', 1)
    pregnancy_day = animal_data.get('pregnancy_day', 0)

    # Basic validation
    if weight_kg <= 0:
        return {
            'maintenance_mp': 0,
            'lactation_mp': 0,
            'pregnancy_mp': 0,
            'growth_mp': 0,
            'total_mp': 0,
            'crude_protein_percent': 0
        }

    # Calculate maintenance protein requirement (MP)
    # NASEM 2021 equation for maintenance MP
    maintenance_mp = 4.1 * (weight_kg**0.5) + 0.3 * weight_kg + 30

    # Calculate lactation protein requirement (MP)
    if milk_kg > 0:
        # NASEM 2021 equation for milk protein
        # Efficiency of MP use for milk protein synthesis is 0.67
        milk_protein_g = milk_kg * milk_protein * 10  # Convert to g/day
        lactation_mp = milk_protein_g / 0.67
    else:
        lactation_mp = 0

    # Calculate pregnancy protein requirement (MP)
    pregnancy_mp = 0
    if pregnancy_day > 190:  # Only significant in last trimester
        # NASEM 2021 equation for pregnancy MP
        # Assumes 45 kg calf birth weight
        calf_birth_weight = 45  # kg
        pregnancy_mp = 0.69 * calf_birth_weight * math.exp(0.0345 * (pregnancy_day - 190))

    # Calculate growth protein requirement (MP)
    growth_mp = 0
    if lactation == 1 and dim > 0:  # First lactation animals still growing
        # NASEM 2021 equation for growth MP during first lactation
        growth_mp = 0.12 * maintenance_mp

    # Calculate total MP requirement
    total_mp = maintenance_mp + lactation_mp + pregnancy_mp + growth_mp

    # Calculate required crude protein percentage based on DMI
    dmi = calculate_dry_matter_intake(animal_data)
    # Assuming MP to CP conversion efficiency of approximately 0.67
    crude_protein_g = total_mp / 0.67
    crude_protein_percent = (crude_protein_g / 1000) / dmi * 100 if dmi > 0 else 0

    return {
        'maintenance_mp': round(maintenance_mp, 0),
        'lactation_mp': round(lactation_mp, 0),
        'pregnancy_mp': round(pregnancy_mp, 0),
        'growth_mp': round(growth_mp, 0),
        'total_mp': round(total_mp, 0),
        'crude_protein_percent': round(crude_protein_percent, 1)
    }


def calculate_mineral_requirements(animal_data):
    """
    Calculate mineral requirements using NRC 8th edition equations

    Args:
        animal_data: Dictionary containing animal characteristics
            - weight_kg: Body weight in kg
            - milk_production_kg: Milk production in kg/day
            - days_in_milk: Days in milk
            - pregnancy_day: Day of pregnancy (if applicable)

    Returns:
        dict: Mineral requirements (% of DM or mg/kg of DM)
            - calcium_percent: Calcium requirement (% of DM)
            - phosphorus_percent: Phosphorus requirement (% of DM)
            - magnesium_percent: Magnesium requirement (% of DM)
            - potassium_percent: Potassium requirement (% of DM)
            - sodium_percent: Sodium requirement (% of DM)
            - chlorine_percent: Chlorine requirement (% of DM)
            - sulfur_percent: Sulfur requirement (% of DM)
            - iron_mg_kg: Iron requirement (mg/kg of DM)
            - manganese_mg_kg: Manganese requirement (mg/kg of DM)
            - zinc_mg_kg: Zinc requirement (mg/kg of DM)
            - copper_mg_kg: Copper requirement (mg/kg of DM)
            - cobalt_mg_kg: Cobalt requirement (mg/kg of DM)
            - iodine_mg_kg: Iodine requirement (mg/kg of DM)
            - selenium_mg_kg: Selenium requirement (mg/kg of DM)
    """
    # Extract required parameters with defaults if not provided
    weight_kg = animal_data.get('weight_kg', 0)
    milk_kg = animal_data.get('milk_production_kg', 0)
    dim = animal_data.get('days_in_milk', 0)
    pregnancy_day = animal_data.get('pregnancy_day', 0)

    # Calculate DMI
    dmi = calculate_dry_matter_intake(animal_data)

    # Basic validation
    if weight_kg <= 0 or dmi <= 0:
        return {
            'calcium_percent': 0,
            'phosphorus_percent': 0,
            'magnesium_percent': 0,
            'potassium_percent': 0,
            'sodium_percent': 0,
            'chlorine_percent': 0,
            'sulfur_percent': 0,
            'iron_mg_kg': 0,
            'manganese_mg_kg': 0,
            'zinc_mg_kg': 0,
            'copper_mg_kg': 0,
            'cobalt_mg_kg': 0,
            'iodine_mg_kg': 0,
            'selenium_mg_kg': 0
        }

    # Calculate mineral requirements based on NASEM 2021

    # Macro minerals (% of DM)
    # Calcium
    if milk_kg > 0:  # Lactating cow
        calcium_g = 0.0078 * weight_kg + (1.2 * milk_kg)
        if pregnancy_day > 190:
            calcium_g += 0.02 * 45 * math.exp(0.0345 * (pregnancy_day - 190))
    else:  # Dry cow or heifer
        calcium_g = 0.0078 * weight_kg
        if pregnancy_day > 190:
            calcium_g += 0.02 * 45 * math.exp(0.0345 * (pregnancy_day - 190))

    calcium_percent = calcium_g / (dmi * 10)

    # Phosphorus
    if milk_kg > 0:  # Lactating cow
        phosphorus_g = 0.01 * weight_kg + (0.9 * milk_kg)
        if pregnancy_day > 190:
            phosphorus_g += 0.01 * 45 * math.exp(0.0345 * (pregnancy_day - 190))
    else:  # Dry cow or heifer
        phosphorus_g = 0.01 * weight_kg
        if pregnancy_day > 190:
            phosphorus_g += 0.01 * 45 * math.exp(0.0345 * (pregnancy_day - 190))

    phosphorus_percent = phosphorus_g / (dmi * 10)

    # Magnesium
    if milk_kg > 0:  # Lactating cow
        magnesium_g = 0.003 * weight_kg + (0.15 * milk_kg)
    else:  # Dry cow or heifer
        magnesium_g = 0.003 * weight_kg

    magnesium_percent = magnesium_g / (dmi * 10)

    # Potassium
    if milk_kg > 0:  # Lactating cow
        potassium_g = 0.038 * weight_kg + (1.5 * milk_kg)
    else:  # Dry cow or heifer
        potassium_g = 0.038 * weight_kg

    potassium_percent = potassium_g / (dmi * 10)

    # Sodium
    if milk_kg > 0:  # Lactating cow
        sodium_g = 0.038 * weight_kg + (0.63 * milk_kg)
    else:  # Dry cow or heifer
        sodium_g = 0.038 * weight_kg

    sodium_percent = sodium_g / (dmi * 10)

    # Chlorine
    if milk_kg > 0:  # Lactating cow
        chlorine_g = 0.024 * weight_kg + (1.15 * milk_kg)
    else:  # Dry cow or heifer
        chlorine_g = 0.024 * weight_kg

    chlorine_percent = chlorine_g / (dmi * 10)

    # Sulfur - fixed percentage of DM
    sulfur_percent = 0.2

    # Micro minerals (mg/kg of DM) - NASEM 2021 recommendations
    iron_mg_kg = 50
    manganese_mg_kg = 40
    zinc_mg_kg = 60
    copper_mg_kg = 15
    cobalt_mg_kg = 0.11
    iodine_mg_kg = 0.5
    selenium_mg_kg = 0.3

    return {
        'calcium_percent': round(calcium_percent, 2),
        'phosphorus_percent': round(phosphorus_percent, 2),
        'magnesium_percent': round(magnesium_percent, 2),
        'potassium_percent': round(potassium_percent, 2),
        'sodium_percent': round(sodium_percent, 2),
        'chlorine_percent': round(chlorine_percent, 2),
        'sulfur_percent': round(sulfur_percent, 2),
        'iron_mg_kg': round(iron_mg_kg, 1),
        'manganese_mg_kg': round(manganese_mg_kg, 1),
        'zinc_mg_kg': round(zinc_mg_kg, 1),
        'copper_mg_kg': round(copper_mg_kg, 1),
        'cobalt_mg_kg': round(cobalt_mg_kg, 2),
        'iodine_mg_kg': round(iodine_mg_kg, 2),
        'selenium_mg_kg': round(selenium_mg_kg, 2)
    }


def calculate_fiber_requirements(animal_data):
    """
    Calculate fiber requirements using NRC 8th edition guidelines

    Args:
        animal_data: Dictionary containing animal characteristics
            - milk_production_kg: Milk production in kg/day
            - days_in_milk: Days in milk

    Returns:
        dict: Fiber requirements (% of DM)
            - ndf_min_percent: Minimum NDF requirement (% of DM)
            - ndf_max_percent: Maximum NDF recommendation (% of DM)
            - forage_ndf_min_percent: Minimum forage NDF (% of DM)
            - adf_min_percent: Minimum ADF requirement (% of DM)
            - peNDF_min_percent: Minimum physically effective NDF (% of DM)
    """
    # Extract required parameters with defaults if not provided
    milk_kg = animal_data.get('milk_production_kg', 0)
    dim = animal_data.get('days_in_milk', 0)

    # Set default values
    ndf_min_percent = 25
    ndf_max_percent = 35
    forage_ndf_min_percent = 19
    adf_min_percent = 17
    peNDF_min_percent = 21

    # Adjust based on production level and stage of lactation
    if milk_kg > 0:  # Lactating cow
        if milk_kg > 40:  # High producing
            ndf_min_percent = 28
            ndf_max_percent = 32
            forage_ndf_min_percent = 18
            adf_min_percent = 19
            peNDF_min_percent = 20
        elif milk_kg > 20:  # Medium producing
            ndf_min_percent = 30
            ndf_max_percent = 35
            forage_ndf_min_percent = 19
            adf_min_percent = 20
            peNDF_min_percent = 21
        else:  # Low producing
            ndf_min_percent = 33
            ndf_max_percent = 40
            forage_ndf_min_percent = 21
            adf_min_percent = 21
            peNDF_min_percent = 23

        # Early lactation adjustments
        if dim < 70:
            ndf_min_percent -= 2
            ndf_max_percent -= 3
    else:  # Dry cow
        ndf_min_percent = 35
        ndf_max_percent = 45
        forage_ndf_min_percent = 28
        adf_min_percent = 22
        peNDF_min_percent = 25

    return {
        'ndf_min_percent': ndf_min_percent,
        'ndf_max_percent': ndf_max_percent,
        'forage_ndf_min_percent': forage_ndf_min_percent,
        'adf_min_percent': adf_min_percent,
        'peNDF_min_percent': peNDF_min_percent
    }


def get_nrc8_requirements(animal_group_id, model_version_id=None):
    """
    Get comprehensive NRC 8th edition nutrient requirements for an animal group

    Args:
        animal_group_id: ID of the animal group
        model_version_id: Optional ID of the NRC model version to use (defaults to active model)

    Returns:
        dict: Complete nutrient requirements for the animal group
    """
    from ..models import AnimalGroup
    from .nrc_coefficient_service import get_active_model_version

    # Get animal group data
    animal_group = AnimalGroup.query.get(animal_group_id)

    if not animal_group:
        return {}

    # Get the active model version if not specified
    if model_version_id is None:
        active_model = get_active_model_version()
        if active_model:
            model_version_id = active_model.id
            logger.info(f"Using active NRC model version: {active_model.name} (ID: {model_version_id})")
        else:
            logger.warning("No active NRC model version found. Using hardcoded coefficients.")
    else:
        logger.info(f"Using specified NRC model version ID: {model_version_id}")

    # Prepare animal data dictionary
    animal_data = {
        'weight_kg': animal_group.weight_kg,
        'milk_production_kg': animal_group.milk_production_kg,
        'milk_fat_percent': 3.5,  # Default value, could be added to the model
        'milk_protein_percent': 3.0,  # Default value, could be added to the model
        'days_in_milk': animal_group.days_in_milk,
        'lactation_number': animal_group.lactation_number,
        'bcs': animal_group.bcs,
        'pregnancy_day': 0,  # Default value, could be added to the model
        'model_version_id': model_version_id  # Pass the model version ID to the calculation functions
    }

    # Calculate all requirements
    dmi = calculate_dry_matter_intake(animal_data)
    energy_reqs = calculate_energy_requirements(animal_data)
    protein_reqs = calculate_protein_requirements(animal_data)
    mineral_reqs = calculate_mineral_requirements(animal_data)
    fiber_reqs = calculate_fiber_requirements(animal_data)

    # Combine all requirements into a single dictionary
    requirements = {
        'dmi_kg': dmi,
        'energy': energy_reqs,
        'protein': protein_reqs,
        'minerals': mineral_reqs,
        'fiber': fiber_reqs
    }

    # Convert requirements to constraints format for the formulation system
    constraints = {}

    # Energy constraints
    constraints['NEL'] = {
        'min': energy_reqs['nel_concentration'],
        'max': None,
        'unit': 'Mcal/kg'
    }

    # Protein constraints
    constraints['Crude Protein'] = {
        'min': protein_reqs['crude_protein_percent'],
        'max': 17,#protein_reqs['crude_protein_percent'] + 2,  # Allow some margin
        'unit': '%'
    }

    # Fiber constraints
    constraints['NDF'] = {
        'min': fiber_reqs['ndf_min_percent'],
        'max': fiber_reqs['ndf_max_percent'],
        'unit': '%'
    }

    constraints['ADF'] = {
        'min': fiber_reqs['adf_min_percent'],
        'max': None,
        'unit': '%'
    }

    # Mineral constraints
    constraints['Calcium'] = {
        'min': mineral_reqs['calcium_percent'],
        'max': None, #mineral_reqs['calcium_percent'] * 1.5,  # Allow some margin
        'unit': '%'
    }

    constraints['Phosphorus'] = {
        'min': mineral_reqs['phosphorus_percent'],
        'max': None,#mineral_reqs['phosphorus_percent'] * 1.5,  # Allow some margin
        'unit': '%'
    }

    constraints['DMI'] = {
        'min': dmi,
        'max': dmi,  # Allow some margin
        'unit': 'kg'
    }

    # Add model version information to the response
    model_info = None
    if model_version_id:
        from ..models import NrcModelVersion
        model = NrcModelVersion.query.get(model_version_id)
        if model:
            model_info = {
                'id': model.id,
                'name': model.name,
                'description': model.description,
                'publication_date': model.publication_date.isoformat() if model.publication_date else None
            }

    return {
        'detailed_requirements': requirements,
        'constraints': constraints,
        'model_version': model_info
    }
